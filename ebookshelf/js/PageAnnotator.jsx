import React from 'react';

// PageAnnotator React Component
// This component uses the LiterallyCanvasReactComponent with built-in GUI
const PageAnnotator = ({
  width = 800,
  height = 600,
  tools = null,
  onDrawingChange = null,
  snapshot = null,
  primaryColor = '#000',
  secondaryColor = '#fff',
  backgroundColor = 'transparent',
  strokeWidths = [1, 2, 5, 10, 20],
  defaultStrokeWidth = 2,
  imageSize = null,
  toolbarPosition = 'top',
  onInit = null
}) => {
  // Use the React GUI component from LiterallyCanvas
  const { LiterallyCanvasReactComponent, tools: lcTools } = window.LC;

  // Default tools if none provided
  const defaultTools = [
    lcTools.Pencil,
    lcTools.Eraser,
    lcTools.Line,
    lcTools.Pan
  ];

  return React.createElement(LiterallyCanvasReactComponent, {
    imageSize: imageSize || { width, height },
    tools: tools || defaultTools,
    toolbarPosition: toolbarPosition,
    snapshot: snapshot,
    onDrawingChange: onDrawingChange,
    onInit: onInit,
    primaryColor: primaryColor,
    secondaryColor: secondaryColor,
    backgroundColor: backgroundColor,
    strokeWidths: strokeWidths,
    defaultStrokeWidth: defaultStrokeWidth
  });

  // Update colors when props change
  useEffect(() => {
    if (lcRef.current) {
      lcRef.current.setColor('primary', primaryColor);
    }
  }, [primaryColor]);

  useEffect(() => {
    if (lcRef.current) {
      lcRef.current.setColor('secondary', secondaryColor);
    }
  }, [secondaryColor]);

  useEffect(() => {
    if (lcRef.current) {
      lcRef.current.setColor('background', backgroundColor);
    }
  }, [backgroundColor]);

  // Expose LC instance methods
  const getSnapshot = () => {
    return lcRef.current ? lcRef.current.getSnapshot() : null;
  };

  const loadSnapshot = (snapshot) => {
    if (lcRef.current) {
      lcRef.current.loadSnapshot(snapshot);
    }
  };

  const clear = () => {
    if (lcRef.current) {
      lcRef.current.clear();
    }
  };

  const undo = () => {
    if (lcRef.current) {
      lcRef.current.undo();
    }
  };

  const redo = () => {
    if (lcRef.current) {
      lcRef.current.redo();
    }
  };

  const setTool = (tool) => {
    if (lcRef.current) {
      lcRef.current.setTool(new tool(lcRef.current));
    }
  };

  const setZoom = (scale) => {
    if (lcRef.current) {
      lcRef.current.setZoom(scale);
    }
  };

  const setPan = (x, y) => {
    if (lcRef.current) {
      lcRef.current.setPan(x, y);
    }
  };

  const getImage = (options = {}) => {
    return lcRef.current ? lcRef.current.getImage(options) : null;
  };

  const getSVGString = () => {
    return lcRef.current ? lcRef.current.getSVGString() : '';
  };

  // Expose methods via ref
  React.useImperativeHandle(React.forwardRef(() => {}), () => ({
    getSnapshot,
    loadSnapshot,
    clear,
    undo,
    redo,
    setTool,
    setZoom,
    setPan,
    getImage,
    getSVGString,
    lc: lcRef.current
  }));

  return (
    <div 
      ref={containerRef}
      style={{ 
        width: width + 'px', 
        height: height + 'px',
        border: '1px solid #ccc',
        position: 'relative'
      }}
      className="literally-canvas-container"
    />
  );
};

export default PageAnnotator;
