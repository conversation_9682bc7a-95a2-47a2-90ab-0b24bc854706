﻿/*
 * loadapp - ebookshelf 2022-06-10
*/

function loadApp() {

	var resized = 0;
	var bookmark = {};
	var pageAnnotatorRef = null;
	var pageAnnotatorContainer = null;
	var testClass = [];
	var testClass2 = [];
	var selectClass = '0';
	
	if ( bookLang == 'eng' ) {
		var displayText = {
			'01':'File cannot be larger than 5 MB.',
			'02':'File is not MP3 or PDF.',
			'03':'System error.',
			'04':'Upload finished.',
			'05':'Network error.',
			'06':'Delete finished.',
			'07':'Add new finished.',
			'08':'Edit',
			'09':'Save',
			'10':'System error: cannot save data.',
			'11':'Error Message',
			'12':'Network error: cannot connect to server.',
			'13':'File upload',
			'14':'Delete',
			'15':'Insert'
		};
	} else {
		var displayText = {
			'01':'檔案太大，檔案不得超過 5 MB。',
			'02':'檔案類型不是 pdf 或者 mp3。',
			'03':'系統錯誤。',
			'04':'上傳完成。',
			'05':'網路錯誤。',
			'06':'刪除完成。',
			'07':'加入成功。',
			'08':'修改',
			'09':'儲存',
			'10':'系統錯誤：無法儲存數據。',
			'11':'錯誤訊息',
			'12':'網路錯誤：無法連接到伺服器。',
			'13':'上傳檔案',
			'14':'刪除',
			'15':'插入'
		};		
	}

	document.addEventListener('gesturestart', function (e) {
		e.preventDefault();
	});
	$( "#lc" ).bind('touchend', function(e) {
		if ( !$(e.target).is("a") ) {
			e.preventDefault();
		}
	});
	$( ".home-icon" ).bind('touchend', function(e) {
		e.preventDefault();
	});
	$( ".zoom-icon" ).bind('touchend', function(e) {
		e.preventDefault();
	});
	$( ".page-icon" ).bind('touchend', function(e) {
		e.preventDefault();
	});
	$( ".content-icon" ).bind('touchend', function(e) {
		e.preventDefault();
	});
	$( ".note-icon" ).bind('touchend', function(e) {
		e.preventDefault();
	});
	$( ".highlighter-icon" ).bind('touchend', function(e) {
		e.preventDefault();
	});
	$( ".bookmark-icon" ).bind('touchend', function(e) {
		e.preventDefault();
	});
	$( ".test-icon" ).bind('touchend', function(e) {
		e.preventDefault();
	});
	$( "#testExcel" ).bind('touchend', function(e) {
		e.preventDefault();
	});
	$( "#testExcel2" ).bind('touchend', function(e) {
		e.preventDefault();
	});


	getBookmark();

	function getBookmark() {
		$.ajax({
			dataType: "json",
			url: '../../load/bookmark.php',
			data: {book_id:bookID},
			type: "POST",
			success: function(data){
				$('#bookmark-content').empty();
				bookmark = {};
				$.each(data, function(key, region) {
					// addRegion(region, element);
					bookmark[region.page] = region.name;
					var html = '<div class="bookmark-to-page" data-page="' + region.page + '">' + region.name + '</div>';
					$('#bookmark-content').append(html);
				});
			}
		});
	}

	function saveBookmark() {
		var pages = '[{';
		var i = 0;
		$.each(bookmark, function( index, value ) {
			// console.log( index + ": " + value );
			if ( i > 0) {
				pages += '},{';
			}
			pages += '"page":"' + index + '","name":"' + value + '"';
			i++;
		});
		pages += '}]';
		if ( pages == '[{}]' ) pages = '';
		$.ajax({
			dataType: "json",
			url: '../../load/bookmark_add.php',
			data: {
				book_id:bookID,
				pages:pages
			},
			type: "POST",
			success: function(data){
				$('#bookmark-content').empty();
				bookmark = {};
				$.each(data, function(key, region) {
					// addRegion(region, element);
					bookmark[region.page] = region.name;
					var html = '<div class="bookmark-to-page" data-page="' + region.page + '">' + region.name + '</div>';
					$('#bookmark-content').append(html);
				});
			}
		});
	}

	$( "#dialogBookmark" ).dialog({
 		autoOpen: false,
		width: 200,
		height: 300,
		resizable: false,
		modal: true,
		draggable: false,
		open: function( event, ui ) {
			$( this ).dialog( "option", "position", [64,334] );
		},
		create: function(event,ui) {
			$( "#dialogBookmark" ).bind('touchend', function(e) {
				e.preventDefault();
			});
		}
	});

	$( "#dialogNotes" ).dialog({
 		autoOpen: false,
		width: 350,
		height: 'auto',
		resizable: false,
		modal: false,
		position: { my: "center", at: "center", of: window },
		open: function( event, ui ) {
			$( this ).dialog( "option", "position", { my: "center", at: "center", of: window } );
		},
		beforeClose: function( event, ui ) {
			$( this ).dialog( "option", "modal", false );
		},
		create: function(event,ui) {
			$( "#dialogNotes" ).bind('touchend', function(e) {
				e.preventDefault();
			});
		}
	});


	function loadFiles() {
		$.ajax({
			dataType: "json",
			url: '../../load/file_list.php',
			type: "POST",
			success: function(data){
				$('#dialogFile table').find('tr:gt(0)').remove();
				// $('#dialogFile').find('.error').text('');
				$.each(data.files, function(key, file) {
					// addRegion(region, element);
					var html = '<tr style="height:40px;"><td style="text-align:center;">'+file.file_id+'</td><td><a href="../../load/files.php?id='+file.file_id+'" target="_blank">';
					html += file.file_name;
					html += '</a></td><td style="text-align:center;">';
					html += file.edit_date;
					html += '</td><td style="text-align:center;"><button class="file-insert-btn" data-id="'+file.file_id+'">'+displayText['15']+'</button></td>';
					html += '<td style="text-align:center;"><button class="file-delete-btn" data-id="'+file.file_id+'">'+displayText['14']+'</button></td></tr>';
					$('#dialogFile table').append(html);
				});
			}
		});
	}

	$( "#dialogFile" ).dialog({
 		autoOpen: false,
		width: '500',
		height: '500',
		resizable: false,
		modal: true,
		position: { my: "center", at: "center", of: window },
		buttons: [],
		open: function( event, ui ) {
			$( this ).dialog( "option", "position", { my: "center", at: "center", of: window } );
			// load
			loadFiles();
		},
		beforeClose: function( event, ui ) {
			$('#dialogFile').find('.error').text('');
		},
		create: function(event,ui) {

			$( "#dialogFile" ).bind('touchend', function(e) {
				if ( !$(e.target).is("input") && !$(e.target).is("a") && !$(e.target).is("button") ) {
					e.preventDefault();
				}
			});

			$( "#dialogFile" ).delegate( '#file-upload', 'click', function() {
				if ( $('#file-content').val() != '' ) {
					$('#dialogFile').find('.error').text('');
					var file = $('#file-content')[0].files[0];

					if(file.name.length < 1) {
						// do nothing
					} else if(file.size > 5000000) {
						$('#dialogFile').find('.error').text( displayText['01'] );
					}
					else if(file.type != 'application/pdf' && file.type != 'audio/mpeg' && file.type != 'audio/mp3' && file.type != 'image/png' && file.type != 'image/jpg'  ) {
						$('#dialogFile').find('.error').text( displayText['02'] );
						// $('#dialogFile').find('.error').text(file.type);
					} else {
						// ajax upload
						var formData = new FormData();
						formData.append('file', file );

						$.ajax({
							url : '../../load/file_upload.php',
							type : 'POST',
							data : formData,
							dataType: "json",
							processData: false,  // tell jQuery not to process the data
							contentType: false,  // tell jQuery not to set contentType
							success : function(data) {
							   if ( !data.success ) {
								   $('#dialogFile').find('.error').text( displayText['03'] );
								   console.log(data.message);
							   } else {
								   $('#file-content').val('');
								   $('#dialogFile').find('.error').text( displayText['04'] );
								   loadFiles();
							   }
							},
							error: function (xhr, ajaxOptions, thrownError) {
								$('#dialogFile').find('.error').text( displayText['05'] );
								console.log(xhr);
								console.log(thrownError);
							}

						});
					}
				}
			});

			$( "#dialogFile" ).delegate( '.file-delete-btn', 'click', function() {
				var fileID = $(this).attr('data-id');

				$.ajax({
					url : '../../load/file_del.php',
					type : 'POST',
					data : {
						file_id: fileID
					},
					dataType: "json",
					success : function(data) {
					   if ( !data.success ) {
						   $('#dialogFile').find('.error').text( displayText['03'] );
						   console.log(data.message);
					   } else {
						   $('#dialogFile').find('.error').text( displayText['06'] );
						   loadFiles();
						   // reload all custom regions
					   }
					},
					error: function (xhr, ajaxOptions, thrownError) {
						$('#dialogFile').find('.error').text( displayText['05'] );
						console.log(xhr);
						console.log(thrownError);
					}

				});

			});

			$( "#dialogFile" ).delegate( '.file-insert-btn', 'click', function() {
				var fileID = $(this).attr('data-id');

				var cid = $( '#dialogCustomNote' ).find('#custom-id').val();
				var data = $( '#dialogCustomNote' ).find('textarea').val() + '[#file' + fileID + ']';

				$.ajax({
					dataType: "json",
					url: '../../load/custom_region_edit.php',
					data: {
						cid: cid,
						data: data
					},
					type: "POST",
					success: function(data){
						if (data.success) {
							$('#dialogFile').find('.error').text( displayText['07'] );
							$( "#dialogCustomNote" ).dialog( "close");
							var element = $('div.p' + data.page);
							// var element = document.getElementsByClassName("p" + data.page);
							loadCustomRegions( data.page, element, data.cid);
						} else {
						   $('#dialogFile').find('.error').text( displayText['03'] );
						   console.log(data.message);
						}
					},
					error: function (xhr, ajaxOptions, thrownError) {
						$('#dialogFile').find('.error').text( displayText['05'] );
						console.log(xhr);
						console.log(thrownError);
					}
				});

			});
		}
	});

	$( "#dialogCustomNote" ).dialog({
 		autoOpen: false,
		width: 'auto',
		height: 'auto',
		minHeight: 300,
		minWidth: 300,
		resizable: false,
		modal: false,
		position: { my: "center", at: "center", of: window },
		buttons: [
			{
				text: displayText['08'],
				click: function(event){

					if ( $(event.delegateTarget).text() == displayText['08']) {
						$( this ).find('p').hide();
						$( this ).find('textarea').show();
						$(event.delegateTarget).find('span').text( displayText['09'] );
					} else {
						var cid = $( this ).find('#custom-id').val();
						var data = $( this ).find('textarea').val();
						$.ajax({
							dataType: "json",
							url: '../../load/custom_region_edit.php',
							data: {
								cid: cid,
								data: data
							},
							type: "POST",
							success: function(data){
								if (data.success) {
									$( "#dialogCustomNote" ).dialog( "close");
									var element = $('div.p' + data.page);
									loadCustomRegions( data.page, element, data.cid);
								} else {
									$( "#dialogNotes" ).find('p').html( displayText['03'] );
									$( "#dialogNotes" ).dialog('option', 'title', displayText['11']);
									$( "#dialogNotes" ).dialog( "option", "modal", true );
									$( "#dialogNotes" ).dialog('open');
								}
							},
							error: function (xhr, ajaxOptions, thrownError) {
								$( "#dialogNotes" ).find('p').html( displayText['05'] );
								$( "#dialogNotes" ).dialog('option', 'title', displayText['11']);
								$( "#dialogNotes" ).dialog( "option", "modal", true );
								$( "#dialogNotes" ).dialog('open');
							}
						});


					}

				}
			},
			{
				text: displayText['13'],
				click: function(event){
					$( "#dialogFile" ).dialog('open');
				}
			},
			{
				text: displayText['14'],
				click: function(){
					var cid = $( this ).find('#custom-id').val();
					$.ajax({
						dataType: "json",
						url: '../../load/custom_region_del.php',
						data: {
							cid: cid
						},
						type: "POST",
						success: function(data){
							if (data.success) {
								$( "#dialogCustomNote" ).dialog( "close");
								var element = $('div.p' + data.page);
								loadCustomRegions( data.page, element, data.cid);
							} else {
								$( "#dialogNotes" ).find('p').html( displayText['03'] );
								$( "#dialogNotes" ).dialog('option', 'title', displayText['11']);
								$( "#dialogNotes" ).dialog( "option", "modal", true );
								$( "#dialogNotes" ).dialog('open');
							}
						},
						error: function (xhr, ajaxOptions, thrownError) {
							$( "#dialogNotes" ).find('p').html( displayText['05'] );
							$( "#dialogNotes" ).dialog('option', 'title', displayText['11']);
							$( "#dialogNotes" ).dialog( "option", "modal", true );
							$( "#dialogNotes" ).dialog('open');
						}
					});

				}
			}
		],
		open: function( event, ui ) {
			$( this ).dialog( "option", "position", { my: "center", at: "center", of: window } );

			if ( customNoteAdd ) {
				$( this ).parent().find('button:first').find('span').text( displayText['09'] );
				$( this ).find('p').hide();
				$( this ).find('textarea').show();
			} else {
				$( this ).parent().find('button:first').find('span').text( displayText['08'] );
				$( this ).find('textarea').hide();
				$( this ).find('p').show();
			}
			$( this ).parent().find('button:first').focus();
			if ( usertype == 2 ) {
				$( this ).parent().find('button:nth-child(2)').hide();
			}			
		},
		beforeClose: function( event, ui ) {
			customNoteAdd = false;
			$( this ).dialog( "option", "modal", false );
		},
		create: function(event,ui) {
			$( "#dialogCustomNote" ).bind('touchend', function(e) {
				if ( !$(e.target).is("a") && !$(e.target).is("input") && !$(e.target).is("textarea") ) {
					e.preventDefault();
				}
			});
		}
	});

	$( "#dialogAudio" ).dialog({
 		autoOpen: false,
		width: 350,
		height: 100,
		resizable: false,
		modal: false,
		open: function( event, ui ) {
			$( this ).dialog( "option", "position", { my: "right top", at: "right top", of: window } );
			players[0].setVolume(5);
			players[0].play();
		},
		beforeClose: function( event, ui ) {
			players[0].stop();
		},
		create: function(event,ui) {
			$( "#dialogAudio" ).bind('click', function(e) {
				if ( !$(e.target).is("button") && !$(e.target).is("input") ) {
					e.preventDefault();
				}
			});
		}
	});

	$( "#dialogVideo" ).dialog({
 		autoOpen: false,
		width:  $(window).width() * 1,
		height: $(window).height() * 1,
		resizable: false,
		modal: true,
		open: function( event, ui ) {
			$( this ).dialog( "option", "position", { my: "center", at: "center", of: window } );
			document.getElementById("myVideo").volume = 0.5;
			document.getElementById("myVideo").play();
		},
		beforeClose: function( event, ui ) {
			document.getElementById("myVideo").pause();
		},
		create: function(event,ui) {
			$( "#dialogVideo" ).bind('touchend', function(e) {
				e.preventDefault();
			});
		}
	});

	$( "#dialogHtml5" ).dialog({
 		autoOpen: false,
		resizable: false,
		modal: true,
		open: function( event, ui ) {
			$( this ).dialog( "option", "position", { my: "center", at: "center", of: window } );
			var dWidth = $("#dialogHtml5").width() + 'px';
			var dHeight = $("#dialogHtml5").height() + 'px';
			$('#myHtml5').css('width',dWidth);
			$('#myHtml5').css('height',dHeight);
		},
		beforeClose: function( event, ui ) {
			$( "#dialogHtml5" ).attr('data-student','');
			$("#myHtml5").attr("src", "")
		},
		create: function(event,ui) {
			$( "#dialogHtml5" ).bind('touchend', function(e) {
				e.preventDefault();
			});
		}
	});
	
	$( "#dialogTest2" ).dialog({
 		autoOpen: false,
		resizable: false,
		modal: true,
		open: function( event, ui ) {
			$( this ).dialog( "option", "width", $(window).width() * 0.8 );
			$( this ).dialog( "option", "height", $(window).height() * 0.8 );
			$( this ).dialog( "option", "position", { my: "center", at: "center", of: window } );
			var dWidth = $("#dialogTest2").width() + 'px';
			var dHeight = $("#dialogTest2").height() + 'px';
			$('#testTable3').css('width',dWidth);
			$('#testTable3').css('height',dHeight);
			
			$("#testWarning2").text("資訊同步中...");
			$("#testWarning2").show();
			
			var page = $('#html5Page').val();
			var number = $('#html5Number').val();
			
			// load data
			$.ajax({
				// async: false,
				dataType: "json",
				type: 'POST',
				url: '/ebookshelf/load/getTests2.php',
				data: {
					'book_id': bookID,
					'page': page,
					'number': number,
				},
				beforeSend: function() {
					
				},
				success: function(response) {
					
					if (response.teacher) {
						
						$("#testWarning2").hide();
						// fill teacher data
						$('#testClass2').children().remove();
												
						jQuery.each(response.classes, function(i, item) {
							$("#testClass2").append('<option value="' + item.class_id + '">' + item.class_name + '</option>');
						});
						
						testClass2 = response.classes;
						
						if ( selectClass == '0' ) {
							selectClass = response.classes[0].class_id;
						}
						
						$("#testClass2").val(selectClass).change();

					}
					
				},
				error: function (xhr,status,error) {
					$("#testWarning2").text("同步失敗，請再嘗試。");
				}
			});
			
		},
		beforeClose: function( event, ui ) {
			if ( $('#html5Src').val() != "" ) {
				$("#myHtml5").attr('src', $('#html5Src').val() );
				$( "#dialogHtml5" ).attr('data-student','');
				$( "#dialogHtml5" ).dialog('open');
			} else {
				// closeDialog();
				$( "#dialogTest" ).dialog( "open" );
			}
		},
		create: function(event,ui) {
			$( "#dialogTest" ).bind('touchend', function(e) {
				e.preventDefault();
			});
		}
	});
			
			
	$( "#dialogTest" ).dialog({
 		autoOpen: false,
		resizable: false,
		modal: true,
		open: function( event, ui ) {
			$( this ).dialog( "option", "width", $(window).width() * 0.8 );
			$( this ).dialog( "option", "height", $(window).height() * 0.8 );
			$( this ).dialog( "option", "position", { my: "center", at: "center", of: window } );
			var dWidth = $("#dialogTest").width() + 'px';
			var dHeight = $("#dialogTest").height() + 'px';
			$('#testTable').css('width',dWidth);
			$('#testTable').css('height',dHeight);
			
			$("#testWarning").text("資訊同步中...");
			$("#testWarning").show();
			
			// load data
			$.ajax({
				// async: false,
				dataType: "json",
				type: 'POST',
				url: '/ebookshelf/load/getTests.php',
				data: {
					'book_id': bookID,
				},
				beforeSend: function() {
					
				},
				success: function(response) {
					if (response.student) {
						$("#testWarning").hide();
						// fill student data
						jQuery.each(response.rows, function(i, item) {
							// console.log(item.test_mark);
							$('#testTable2 tr').each(function() {
								// if ( ($( this ).find('td:nth-child(1)').attr('data') == item.page) && ($( this ).find('td:nth-child(2)').text() == item.number) ) {
								if ( parseInt($( this ).attr('data-page')) == item.page && parseInt($( this ).attr('data-number')) == item.number ) {
									if ( item.total_mark == 0 ) {
										if ( bookLang == 'eng' ) {
											$( this ).find('td:nth-child(4)').text( "submitted" );
										} else {
											$( this ).find('td:nth-child(4)').text( "已提交" );
										}
										$( this ).find('td:nth-child(4)').css("color","green");
									} else {
										$( this ).find('td:nth-child(4)').text( item.test_mark + ' / ' + item.total_mark );
										$( this ).find('td:nth-child(4)').css("color","green");
									}
								}
							});
						});
					} else if (response.teacher) {
						$("#testWarning").hide();
						// fill teacher data
						$('#testClass').children().remove();
												
						jQuery.each(response.classes, function(i, item) {
							$("#testClass").append('<option value="' + item.class_id + '">' + item.class_name + '</option>');
						});
						
						testClass = response.classes;
						
						if ( selectClass == '0' ) {
							selectClass = response.classes[0].class_id;
						}
						
						$("#testClass").val(selectClass).change();

						
					} else {
						$("#testWarning").text("同步失敗，請再嘗試。");
					}
				},
				error: function (xhr,status,error) {
					$("#testWarning").text("同步失敗，請再嘗試。");
				}
			});

			
			
		},
		beforeClose: function( event, ui ) {
			
		},
		create: function(event,ui) {
			$( "#dialogTest" ).bind('touchend', function(e) {
				e.preventDefault();
			});
			
		}
	});
	
	$('#testExcel').bind('click touchstart', function(e) {
		e.preventDefault();
		e.stopImmediatePropagation();
		var te = new TableExport(document.getElementById('testTable2'),{formats: ['xlsx'], filename: "resultCount", exportButtons: false, ignoreCols: 4});
		var exportData = te.getExportData()['testTable2']['xlsx'];
		console.log(exportData);
		te.export2file(exportData.data, exportData.mimeType, exportData.filename, exportData.fileExtension);
	});
	
	$('#testExcel2').bind('click touchstart', function(e) {
		e.preventDefault();
		e.stopImmediatePropagation();
		var te = new TableExport(document.getElementById('testTable4'),{formats: ['xlsx'], filename: "resultScore", exportButtons: false, ignoreCols: 6});
		var exportData = te.getExportData()['testTable4']['xlsx'];
		console.log(exportData);
		te.export2file(exportData.data, exportData.mimeType, exportData.filename, exportData.fileExtension);
	});
	
	$('select#testClass').change(function(){
		selectClass = $('#testClass').val();
		$.each(testClass, function(i, item) {
			if ( item.class_id == $('#testClass').val() ) {
				
				$('#testTable2 tr').each(function() {
					$( this ).find('td:nth-child(4)').text( '0 / ' + item.size ).css("color","blue");
				});
				
				jQuery.each(item.rows, function(i2, item2) {
					$('#testTable2 tr').each(function() {
						// if ( ($( this ).find('td:nth-child(1)').attr('data') == item2.page) && ($( this ).find('td:nth-child(2)').text() == item2.number) ) {
						if ( ( parseInt($( this ).attr('data-page')) == item2.page) && ( parseInt($( this ).attr('data-number')) == item2.number) ) {
							$( this ).find('td:nth-child(4)').text( item2.count + ' / ' + item.size );
							if ( item2.count == item.size ) {
								$( this ).find('td:nth-child(4)').css("color","green");
							}
						}
					});
				});
				
			}
		});
	});
	
	$('select#testClass2').change(function(){
		selectClass = $('#testClass').val();
		var page = $('#html5Page').val();
		var number = $('#html5Number').val();

		$.each(testClass2, function(i, item) {

			if ( item.class_id == $('#testClass2').val() ) {

				$('#testTable4').empty();
				if ( bookLang == 'eng' ) {
					$('#testTable4').append('<tr><th>Class</th><th>Login</th><th>Name</th><th>Page</th><th>Test</th><th> </th><th></th></tr>');
				} else {
					$('#testTable4').append('<tr><th>班級</th><th>登入</th><th>名稱</th><th>頁數</th><th>測驗</th><th> </th><th></th></tr>');
				}
				
				jQuery.each(item.students, function(i2, item2) {
					if ( bookLang == 'eng' ) {
						$('#testTable4').append('<tr><td class="tableexport-string" >' + item.class_name + '</td><td class="tableexport-string" >' + item2.student_id + '</td><td class="tableexport-string" >' + item2.student_name + '</td><td class="tableexport-string" >' + page + '</td><td class="tableexport-string" >' + number + '</td><td class="tableexport-string" style="color:blue;">not submitted</td><td class="tableexport-string" >---</td></tr>');
					} else {
						$('#testTable4').append('<tr><td class="tableexport-string" >' + item.class_name + '</td><td class="tableexport-string" >' + item2.student_id + '</td><td class="tableexport-string" >' + item2.student_name + '</td><td class="tableexport-string" >' + page + '</td><td class="tableexport-string" >' + number + '</td><td class="tableexport-string" style="color:blue;">未完成</td><td class="tableexport-string" >---</td></tr>');
					}
					
				});
				
				jQuery.each(item.results, function(i2, item2) {
					$('#testTable4 tr').each(function() {
						if ( ($( this ).find('td:nth-child(2)').text() == item2.student_id) ) {
							if ( item2.total_mark == 0 ) {
								$( this ).find('td:nth-child(6)').html( '<p style="max-width:350px;">' + item2['test_result'][0] + '</p>' );
							} else {
								$( this ).find('td:nth-child(6)').text( item2.test_mark + ' / ' + item2.total_mark );
							}
							$( this ).find('td:nth-child(6)').css("color","green");
							if ( bookLang == 'eng' ) {
								$( this ).find('td:nth-child(7)').html('<a class="test3" data-name="' + item2.student_name + '" data-src="html5/p' + pad(page,2) + pad2(number) + '" data-student="' + item2.student_id + '">View answers</a>');
							} else {
								$( this ).find('td:nth-child(7)').html('<a class="test3" data-name="' + item2.student_name + '" data-src="html5/p' + pad(page,2) + pad2(number) + '" data-student="' + item2.student_id + '">檢視答案</a>');
							}							
						}
					});
				});
				
			}			
		});
		
	});

 	$('#canvas').fadeIn(1000);

 	var flipbook = $('.magazine');

 	// Check if the CSS was already loaded

	if (flipbook.width()==0 || flipbook.height()==0) {
		setTimeout(loadApp, 10);
		return;
	}

	// Create the flipbook

	flipbook.turn({

			// Magazine width

			width: flipBookWidth,

			// Magazine height

			height: flipBookHeight,

			// Duration in millisecond

			duration: 1000,

			// Enables gradients

			gradients: true,

			// Auto center this flipbook

			autoCenter: true,

			// Elevation from the edge of the flipbook when turning a page

			elevation: 50,

			// The number of pages

			pages: flipBookPages,

			// display

			display: flipBookDisplay,

			// Events

			when: {
				turning: function(event, page, view) {

					var book = $(this),
					currentPage = book.turn('page'),
					pages = book.turn('pages');

					// Update the current URI

					Hash.go('page/' + page).update();

					// Show and hide navigation buttons

					disableControls(page);

				},

				turned: function(event, page, view) {

					// console.log('turned');
					disableControls(page);

					$(this).turn('center');

					// $('#slider').slider('value', getViewNumber($(this), page));
					// $('#slider').slider('value', page);

					if (page==1) {
						$(this).turn('peel', 'br');
					}

				},

				end: function(event, pageObject, turned){
					// console.log('end');
					$('#slider').slider('value', pageObject.next);
				},

				missing: function (event, pages) {

					// Add pages that aren't in the magazine

					for (var i = 0; i < pages.length; i++)
						addPage(pages[i], $(this));

				}
			}

	});

	// Zoom.js

	$('.magazine-viewport').zoom({
		flipbook: $('.magazine'),

		max: function() {
			return zoomMax;
		},
		// max: 2,

		when: {
			swipeLeft: function() {

				// $(this).zoom('flipbook').turn('next');
				// $('.magazine').turn('next');
				if ( $('.magazine').turn("display") == "single" ) {
					$('.magazine').turn('next').turn('stop');
				} else {
					$('.magazine').turn('next');
				}

			},

			swipeRight: function() {

				// $(this).zoom('flipbook').turn('previous');
				if ( $('.magazine').turn("display") == "single" ) {
					$('.magazine').turn('previous').turn('stop');
				} else {
					$('.magazine').turn('previous');
				}

			},

			resize: function(event, scale, page, pageElement) {

				if (scale==1)
					loadSmallPage(page, pageElement);
				else
					loadLargePage(page, pageElement);

			},

			zoomIn: function () {

				$('#slider-bar').hide();
				$('.made').hide();
				$('.magazine').removeClass('animated').addClass('zoom-in');
				// $('.zoom-icon').removeClass('zoom-icon-in').addClass('zoom-icon-out');

				if ( !$.isTouch ) {

					$('<div />', {'class': 'exit-message'}).
						html('<div>Press ESC to exit</div>').
							appendTo($('body')).
							delay(2000).
							animate({opacity:0}, 500, function() {
								$(this).remove();
							});
				}
			},

			zoomOut: function () {

				$('#slider-bar').fadeIn();
				$('.exit-message').hide();
				$('.made').fadeIn();
				// $('.zoom-icon').removeClass('zoom-icon-out').addClass('zoom-icon-in');

				setTimeout(function(){
					$('.magazine').addClass('animated').removeClass('zoom-in');
					if ( resized == 0 ) {
						resizeViewport();
					} else {
						if ( $(window).height() > $(window).width() ) {
							turnSingle(flipbook);
						} else {
							turnDouble(flipbook);
						}
						resized = 0;
					}
				}, 0);

			}
		}
	});

	// Zoom event

	if ($.isTouch) {
		$('.magazine-viewport').bind('zoom.doubleTap', zoomTo);
	} else {
		$('.magazine-viewport').bind('zoom.tap', zoomTo);
	}


	// Using arrow keys to turn the page

	$(document).keydown(function(e){

		var previous = 37, next = 39, esc = 27;

		switch (e.keyCode) {
			case previous:
				if ( !$('#dialogCustomNote').dialog('isOpen') && !isAnnotationModeActive ) {
					e.preventDefault();
					// left arrow
					if ( $('.magazine').turn("display") == "single" ) {
						$('.magazine').turn('previous').turn('stop');
					} else {
						$('.magazine').turn('previous');
					}
				}
			break;
			case next:
				if ( !$('#dialogCustomNote').dialog('isOpen') && !isAnnotationModeActive ) {
					e.preventDefault();
					//right arrow
					if ( $('.magazine').turn("display") == "single" ) {
						$('.magazine').turn('next').turn('stop');
					} else {
						$('.magazine').turn('next');
					}
				}
			break;
			case esc:
				if (!isAnnotationModeActive) {
					e.preventDefault();
					$('.magazine-viewport').zoom('zoomOut');
				}
			break;
		}
	});

	// URIs - Format #/page/1

	Hash.on('^page\/([0-9]*)$', {
		yep: function(path, parts) {
			var page = parts[1];

			if (page!==undefined) {
				if ($('.magazine').turn('is')) {
					if ( $('.magazine').turn("display") == "single" ) {
						$('.magazine').turn('page', page).turn('stop');
					} else {
						$('.magazine').turn('page', page);
					}
				}
			}

		},
		nop: function(path) {

			if ($('.magazine').turn('is'))
				$('.magazine').turn('page', 1);
		}
	});

	$(window).resize(function() {
		
		if( $(document.activeElement).prop('type') === 'textarea' || $(document.activeElement).prop('type') === 'text' || document.activeElement.tagName == 'IFRAME' ) {
			// do nothing 
		} else {
			if ( $('.magazine-viewport').zoom("value")>1 ) {
				resized = 1;
				$('.magazine-viewport').zoom('zoomOut');
			} else {
				if ( $(window).height() > $(window).width() ) {
					turnSingle(flipbook);
				} else {
					turnDouble(flipbook);
				}
			}
		}
	});

	// Regions

	if ($.isTouch) {
		$('.magazine').bind('touchstart click', regionClick);
	} else {
		$('.magazine').click(regionClick);
	}

	// Events for the next button

	$('.next-button').bind($.mouseEvents.over, function(event) {

		$(this).addClass('next-button-hover');

	}).bind($.mouseEvents.out, function(event) {

		$(this).removeClass('next-button-hover');

	}).bind($.mouseEvents.down, function(event) {

		$(this).addClass('next-button-down');

	}).bind($.mouseEvents.up, function(event) {

		$(this).removeClass('next-button-down');

	}).click(function(event) {

		event.preventDefault();

		// Don't allow navigation during annotation mode
		if (isAnnotationModeActive) return;

		if ( $('.magazine').turn("display") == "single" ) {
			$('.magazine').turn('next').turn('stop');
		} else {
			$('.magazine').turn('next');
		}

	});

	// Events for the previous button

	$('.previous-button').bind($.mouseEvents.over, function() {

		$(this).addClass('previous-button-hover');

	}).bind($.mouseEvents.out, function() {

		$(this).removeClass('previous-button-hover');

	}).bind($.mouseEvents.down, function() {

		$(this).addClass('previous-button-down');

	}).bind($.mouseEvents.up, function() {

		$(this).removeClass('previous-button-down');

	}).click(function(event) {

		event.preventDefault();

		// Don't allow navigation during annotation mode
		if (isAnnotationModeActive) return;

		if ( $('.magazine').turn("display") == "single" ) {
			$('.magazine').turn('previous').turn('stop');
		} else {
			$('.magazine').turn('previous');
		}
	});


	// Slider

	$( "#slider" ).slider({
		min: 1,
		max: numberOfViews(flipbook),

		start: function(event, ui) {

			if (!window._thumbPreview) {
				_thumbPreview = $('<div />', {'class': 'thumbnail'}).html('<div><span>10-101</span></div>');
				setPreview(ui.value);
				_thumbPreview.appendTo($(ui.handle));
			} else
				setPreview(ui.value);

			moveBar(false);

		},

		slide: function(event, ui) {

			setPreview(ui.value);

		},

		stop: function() {

			if (window._thumbPreview)
				_thumbPreview.removeClass('show');

			// Don't allow navigation during annotation mode
			if (isAnnotationModeActive) return;

			// $('.magazine').turn('page', Math.max(1, $(this).slider('value')*2 - 2));
			if ( $('.magazine').turn("display") == "single" ) {
				$('.magazine').turn('page', Math.max(1, $(this).slider('value') )).turn('stop');;
			} else {
				$('.magazine').turn('page', Math.max(1, $(this).slider('value') ));
			}

		}
	});

	resizeViewport();

	$('.magazine').addClass('animated');


	// Zoom icon

	 $('.zoom-icon').bind('mouseover', function() {

		if ($(this).hasClass('zoom-icon-in'))
			$(this).addClass('zoom-icon-in-hover');

		if ($(this).hasClass('zoom-icon-out'))
			$(this).addClass('zoom-icon-out-hover');

	 }).bind('mouseout', function() {

		 if ($(this).hasClass('zoom-icon-in'))
			$(this).removeClass('zoom-icon-in-hover');

		if ($(this).hasClass('zoom-icon-out'))
			$(this).removeClass('zoom-icon-out-hover');

	 }).bind('click touchstart', function() {

		// Don't allow zoom during annotation mode
		if (isAnnotationModeActive) return;

		if ($(this).hasClass('zoom-icon-in')) {

			if ( $('.magazine-viewport').zoom("value")==1 ) {
				zoomMax = 1.5;
				$('.magazine-viewport').zoom('zoomIn');
			} else if ( $('.magazine-viewport').zoom("value")==1.5 ) {
				$('.magazine-viewport').zoom('zoomOut',0);
				zoomMax = 2;
				$('.magazine-viewport').zoom('zoomIn');
			} else if ( $('.magazine-viewport').zoom("value")==2 ) {
				$('.magazine-viewport').zoom('zoomOut',0);
				zoomMax = 2.5;
				$('.magazine-viewport').zoom('zoomIn');
			} else {
				$('.magazine-viewport').zoom('zoomOut');
			}

		} else if ($(this).hasClass('zoom-icon-out')) {
			$('.magazine-viewport').zoom('zoomOut');
		}

	 });

	// page icon single page double page
	 $('.page-icon').bind('click touchstart', function() {

		// Don't allow page mode changes during annotation mode
		if (isAnnotationModeActive) return;

		if ( $('.magazine-viewport').zoom("value")==1 ) {
			if ($(this).hasClass('page-icon-double')) {
				turnSingle(flipbook);
			} else if ($(this).hasClass('page-icon-single')) {
				turnDouble(flipbook);
			}
		}

	 });

	 // go back to ebookshelf
	 $('.home-icon').bind('click touchstart', function() {
		window.location = "../../";
	 });

	 // go to table of content page
	 $('.content-icon').bind('click touchstart', function() {
		// Don't allow navigation during annotation mode
		if (isAnnotationModeActive) return;

		$('.magazine').turn('page', contentPage);
	 });
	 
	// Global variable to track annotation mode
	var isAnnotationModeActive = false;

	// Function to disable page interactions during annotation
	function disablePageInteractions() {
		isAnnotationModeActive = true;

		// Disable zoom functionality
		$('.magazine-viewport').off('zoom.tap zoom.doubleTap');

		// Disable page turning
		$('.magazine').turn("disable", true);

		// Hide navigation controls
		$('#slider-bar').hide();
		$('.next-button').hide();
		$('.previous-button').hide();
		$('.zoom-icon').hide();
		$('.page-icon').hide();

		// Keyboard navigation is handled by the main keydown handler with isAnnotationModeActive check

		// Disable swipe gestures by temporarily removing zoom handlers
		$('.magazine-viewport').data('zoom-disabled', true);

		// Add visual indicator
		$('body').addClass('annotation-mode-active');

		console.log('Page interactions disabled for annotation mode');
	}

	// Function to enable page interactions after annotation
	function enablePageInteractions() {
		isAnnotationModeActive = false;

		// Re-enable zoom functionality
		if ($.isTouch) {
			$('.magazine-viewport').bind('zoom.doubleTap', zoomTo);
		} else {
			$('.magazine-viewport').bind('zoom.tap', zoomTo);
		}

		// Re-enable page turning
		$('.magazine').turn("disable", false);

		// Show navigation controls
		$('#slider-bar').show();
		$('.next-button').show();
		$('.previous-button').show();
		$('.zoom-icon').show();
		$('.page-icon').show();

		// Keyboard navigation is handled by the main keydown handler with isAnnotationModeActive check

		// Re-enable swipe gestures
		$('.magazine-viewport').removeData('zoom-disabled');

		// Remove visual indicator
		$('body').removeClass('annotation-mode-active');

		console.log('Page interactions enabled');
	}

	// PageAnnotator React component functions
	function createPageAnnotator() {
		var currentPage = $(".magazine").turn("page");
		var winHeight = $( '.magazine' ).height();
		var winWidth = $( '.magazine' ).width();

		// Disable page interactions when entering annotation mode
		disablePageInteractions();

		// Create container for PageAnnotator
		pageAnnotatorContainer = $('<div id="page-annotator"></div>');
		pageAnnotatorContainer.css({
			position: 'fixed',
			top: $( '.magazine' ).offset().top,
			left: $( '.magazine' ).offset().left,
			width: winWidth + 80,
			height: winHeight,
			zIndex: 9999,
			backgroundColor: 'rgba(0,0,0,0.8)',
			backgroundImage: 'url(pages/' + currentPage + '.jpg)',
			backgroundSize: 'contain',
			backgroundRepeat: 'no-repeat',
			backgroundPosition: 'center'
		});

		// Add close button
		var closeButton = $('<button id="annotator-close" style="position:absolute;top:10px;right:10px;z-index:10000;padding:10px;background:#fff;border:none;cursor:pointer;">✕</button>');
		pageAnnotatorContainer.append(closeButton);

		$('body').append(pageAnnotatorContainer);

		// Get existing snapshot if available
		var existingSnapshot = null;
		if ( $(".p" + currentPage).find('.custom-draw').length == 1  ) {
			existingSnapshot = JSON.parse( $(".p" + currentPage).find('.custom-draw').attr('region-object') );
		}

		// Create React component
		var annotatorProps = {
			width: flipBookSingleWidth,
			height: flipBookHeight,
			tools: [LC.tools.Pencil, LC.tools.Eraser, LC.tools.Line, LC.tools.Pan],
			toolbarPosition: 'top',
			primaryColor: 'hsla(60, 100%, 50%, 0.5)',
			strokeWidths: [3, 15, 25],
			defaultStrokeWidth: 15,
			snapshot: existingSnapshot,
			onDrawingChange: function() {
				// Auto-save on drawing change
				savePageAnnotatorData();
			},
			onInit: function(lc) {
				pageAnnotatorRef = lc;
				// Set default tool to highlighter (Line tool with yellow color)
				lc.setTool(new LC.tools.Line(lc));
				lc.setColor('primary', 'hsla(60, 100%, 50%, 0.5)');
				lc.setZoom( winHeight / flipBookHeight );
			}
		};

		// Render React component with toolbar
		ReactDOM.render(React.createElement(PageAnnotator, annotatorProps), pageAnnotatorContainer[0]);

		// Close button handler
		closeButton.click(function() {
			closePageAnnotator();
		});

		// ESC key handler
		$(document).on('keydown.annotator', function(e) {
			if (e.keyCode === 27) { // ESC key
				closePageAnnotator();
			}
		});
	}

	function savePageAnnotatorData() {
		if (!pageAnnotatorRef) return;

		var currentPage = $(".magazine").turn("page");
		if ( $(".p" + currentPage).find('.custom-draw').length == 1  ) {
			var id = $(".p" + currentPage).find('.custom-draw').attr('region-id');
			if ( pageAnnotatorRef.getSnapshot().shapes.length > 0 ) {
				// update
				saveCanvas(2, id, pageAnnotatorRef.getImage().toDataURL(), JSON.stringify(pageAnnotatorRef.getSnapshot()));
			} else {
				// delete
				saveCanvas(3, id, '', '');
			}
		} else {
			if ( pageAnnotatorRef.getSnapshot().shapes.length > 0 ) {
				// add new
				saveCanvas(1, 0, pageAnnotatorRef.getImage().toDataURL(), JSON.stringify(pageAnnotatorRef.getSnapshot()));
			}
		}
	}

	function closePageAnnotator() {
		// Save data before closing
		savePageAnnotatorData();

		// Clean up
		if (pageAnnotatorRef) {
			pageAnnotatorRef.teardown();
			pageAnnotatorRef = null;
		}

		if (pageAnnotatorContainer) {
			pageAnnotatorContainer.remove();
			pageAnnotatorContainer = null;
		}

		// Remove ESC key handler
		$(document).off('keydown.annotator');

		// Re-enable page interactions when exiting annotation mode
		enablePageInteractions();
	}
	
	$('.highlighter-icon').bind('click touchstart', function() {

		// check if not zoom
		if ( $('.magazine-viewport').zoom("value")==1 ) {
			closeDialog();

			if ( $('.page-icon').hasClass('page-icon-double') ) {
				turnSingle(flipbook);
			}

			var currentPage = $(".magazine").turn("page");

			// Toggle PageAnnotator: if already open, close it; if closed, open it
			if (pageAnnotatorContainer && pageAnnotatorRef) {
				// PageAnnotator is open, close it
				closePageAnnotator();
			} else {
				// PageAnnotator is closed, open it
				createPageAnnotator();
				saveRegionLog(bookID,'click on custom draw', currentPage ,'custom-draw');
			}
		}
	});

	// Old modal handlers removed - now using React-based PageAnnotator

	// bookmark funtions
	$('.bookmark-icon').bind('click touchstart', function() {
		closeDialog();
		$( "#dialogBookmark" ).dialog( "open" );
	});
	
	$('.test-icon').bind('click touchstart', function() {
		closeDialog();
		$( "#dialogTest" ).dialog( "open" );
	});
	
	$('#bookmark-del').bind('click touchstart', function() {
		var currentPage = $(".magazine").turn("page");
		if ( currentPage in bookmark ) {
			delete bookmark[currentPage];
			saveBookmark();
		}
	});

	$('#bookmark-add').bind('click touchstart', function() {
		var currentPage = $(".magazine").turn("page");
		if ( !(currentPage in bookmark) ) {
			bookmark[currentPage] = pageName[currentPage];
			saveBookmark();
		}
	});

	// $('#file-upload').bind('click touchstart', function() {

	$('#bookmark-content').delegate( '.bookmark-to-page', 'click touchstart', function() {
		if ( $('.magazine').turn("display") == "single" ) {
			$('.magazine').turn('page', $(this).attr('data-page') ).turn('stop');;
		} else {
			$('.magazine').turn('page', $(this).attr('data-page') );
		}
		closeDialog();
	});

	$('#dialogTest').on('click touchstart', 'a.test2', function() {
		closeDialog();
		var page = $(this).parent().parent().attr('data-page');
		var number = $(this).parent().parent().attr('data-number');
		$('#html5Src').val('');
		$('#html5Page').val(page);
		$('#html5Number').val(number);
		$('#dialogTest2').dialog('open');
	});
	
	$('#dialogTest2').on('click touchstart','a.test3', function() {
		var title = "檢視答案 - " + $(this).attr('data-student');
		var src = "../../book/" + bookPath + "/" + $(this).attr('data-src');
		var student = $(this).attr('data-student');
		$("#myHtml5").attr('src',src);
		$( "#dialogHtml5" ).dialog('option', 'title', title);
		
		var adjustPercent = 0.8; // percentage default 0.95
		var adjustTitle = 20;  // pixels default 33
		
		if ( $(window).width() / $(window).height() > 1.33 ) {
			var wHeight = parseInt( $(window).height() * adjustPercent ) -adjustTitle ;
			$( "#dialogHtml5" ).dialog('option', 'width', parseInt( (wHeight - adjustTitle) * 1.33) );
			$( "#dialogHtml5" ).dialog('option', 'height', wHeight );
		} else {
			$( "#dialogHtml5" ).dialog('option', 'width', parseInt($(window).width() * adjustPercent ) );
			$( "#dialogHtml5" ).dialog('option', 'height', parseInt($(window).width() * adjustPercent * 0.75) + adjustTitle );
		}
		
		$( "#dialogHtml5" ).attr('data-student',student);
		$( "#dialogHtml5" ).dialog('open');
	});
	
	$('a.testHtml5').bind('click touchstart', function() {
		closeDialog();
		var title = $(this).text();
		if ( $(this).parent().parent().attr('data-number') == '1' ) {
			var filename = 'p' + $(this).parent().parent().attr('data-page');
		} else {
			var filename = 'p' + $(this).parent().parent().attr('data-page') + '-' + $(this).parent().parent().attr('data-number');
		}
		
		document.getElementById("myHtml5").src = "../../book/" + bookPath + "/html5/" + filename + "/";
		$( "#dialogHtml5" ).dialog('option', 'title', title);
		
		var adjustPercent = 1; // percentage default 0.95
		var adjustTitle = 20;  // pixels default 33
		
		if ( $(window).width() / $(window).height() > 1.33 ) {
			var wHeight = parseInt( $(window).height() * adjustPercent ) -adjustTitle ;
			$( "#dialogHtml5" ).dialog('option', 'width', parseInt( (wHeight - adjustTitle) * 1.33) );
			$( "#dialogHtml5" ).dialog('option', 'height', wHeight );
		} else {
			$( "#dialogHtml5" ).dialog('option', 'width', parseInt($(window).width() * adjustPercent ) );
			$( "#dialogHtml5" ).dialog('option', 'height', parseInt($(window).width() * adjustPercent * 0.75) + adjustTitle );
		}
		
		$( "#dialogHtml5" ).attr('data-student','');
		$( "#dialogHtml5" ).dialog('open');
		
	});
	
	
	$('.note-icon').bind('click touchstart', function() {
		// console.log( $('.page').css('cursor') );
		if ( $('.page').css('cursor') == 'auto' || $('.page').css('cursor') == 'default') {
			disableZoom = true;
			$('#slider-bar').hide();
			$('.next-button').hide();
			$('.previous-button').hide();
			$('.magazine').turn("disable", true);
			$('.page').css('cursor','url(../../pics/note-3-icons.png),context-menu');
			$('.page').bind('click',function(event){

				if ( $(event.target).hasClass('region') || $(event.target).hasClass('custom') ) {
					event.preventDefault;
				} else {
					// add new notes to database
					customNoteAdd = true;
					var page = $.trim($(event.delegateTarget).attr('class').replace('page', '').replace('odd', '').replace('even', '').replace('p', ''));
					var element = event.delegateTarget;
					$.ajax({
						dataType: "json",
						url: '../../load/custom_region_add.php',
						data: {
							book_id:bookID,
							page:page,
							class:'custom-note',
							x: Math.round( event.offsetX / event.delegateTarget.offsetWidth * 1000) / 10 + '%',
							y: Math.round( event.offsetY / event.delegateTarget.offsetHeight * 1000) / 10 + '%',
							width: '5%',
							height: '5%'
						},
						type: "POST",
						success: function(data){
							// unbind click and return to normal
							disableZoom = false;
							$('#slider-bar').fadeIn();
							$('.next-button').show();
							$('.previous-button').show();
							$('.page').css('cursor','auto');
							$('.magazine').turn("disable", false);
							$('.page').unbind('click');

							if (data.success) {
								loadCustomRegions(page, element, data.new_id);
							}
						}
					});
				}

			});
		} else {
			disableZoom = false;
			customNoteAdd = false;
			$('#slider-bar').fadeIn();
			$('.next-button').show();
			$('.previous-button').show();
			$('.page').css('cursor','auto');
			$('.magazine').turn("disable", false);
			$('.page').unbind('click');
		}
	});

}

function openTestDialog2(src,page,number) {
	$( ".eBookDialog" ).each( function() {
		if ($(this).dialog('isOpen') === true) {
			$(this).dialog('close');
		}
	} );
	$('#html5Src').val(src);
	$('#html5Page').val(page);
	$('#html5Number').val(number);
	$('#dialogTest2').dialog('open');
}

function pad(num, size) {
    var s = num+"";
    while (s.length < size) s = "0" + s;
    return s;
}

function pad2(num) {
	if ( num == 1 ) {
		return "";
    } else {
		return "-" + num;
	}
}